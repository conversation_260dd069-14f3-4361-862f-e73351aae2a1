import { DefaultTheme, ThemeProvider } from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack, Redirect } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { AppRegistry } from "react-native";
import { Provider } from "react-redux";
import { useAppDispatch } from "@/store/hooks";
import { PersistGate } from "redux-persist/integration/react";

// Import AuthProvider
import { AuthProvider } from "@/contexts/AuthContext";

// Import Redux store and persistor
import { store, persistor } from "@/store";
import { fetchPaymentMethods } from "@/store/slices/paymentMethodsSlice";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Register the main component
AppRegistry.registerComponent("main", () => RootLayout);

// Component to handle app initialization
function AppInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Fetch payment methods during app initialization
    dispatch(fetchPaymentMethods());
  }, [dispatch]);

  return <>{children}</>;
}

export default function RootLayout() {
  const [loaded] = useFonts({
    Nunito: require("../assets/fonts/Nunito.ttf"),
    NunitoItalic: require("../assets/fonts/Nunito-Italic.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AppInitializer>
          <AuthProvider>
            <ThemeProvider value={DefaultTheme}>
              <Stack
                screenOptions={{
                  headerStyle: {
                    backgroundColor: "#fff",
                  },
                  headerTintColor: "#000",
                  headerTitleStyle: {
                    fontWeight: "bold",
                  },
                  headerShown: false, // Ẩn debug header cho tất cả các màn hình
                }}
              >
                {/* Root and main tabs */}
                <Stack.Screen name="index" />
                <Stack.Screen name="(tabs)" />
                <Stack.Screen name="+not-found" />

                {/* Authentication screens */}
                <Stack.Screen name="auth" options={{ gestureEnabled: false }} />
                <Stack.Screen
                  name="login-phone"
                  options={{ gestureEnabled: true }}
                />
                <Stack.Screen
                  name="login-password"
                  options={{ gestureEnabled: true }}
                />
                <Stack.Screen
                  name="register"
                  options={{ gestureEnabled: true }}
                />
                <Stack.Screen
                  name="forgot-password"
                  options={{ gestureEnabled: true }}
                />
                <Stack.Screen
                  name="verify-otp"
                  options={{ gestureEnabled: true }}
                />
                <Stack.Screen
                  name="new-password"
                  options={{ gestureEnabled: true }}
                />

                {/* Service screens - Local (trong thành phố) */}
                <Stack.Screen
                  name="services/local"
                  options={{
                    gestureEnabled: true,
                    presentation: "card",
                  }}
                />
                <Stack.Screen
                  name="services/local-about"
                  options={{
                    gestureEnabled: true,
                    presentation: "fullScreenModal",
                  }}
                />
                <Stack.Screen
                  name="services/local-promotion"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />
                <Stack.Screen
                  name="services/local-notes"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />

                {/* Service screens - Province (đi tỉnh) */}
                <Stack.Screen
                  name="services/province"
                  options={{
                    gestureEnabled: true,
                    presentation: "card",
                  }}
                />
                <Stack.Screen
                  name="services/province-about"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />
                <Stack.Screen
                  name="services/province-promotion"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />
                <Stack.Screen
                  name="services/province-notes"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />

                {/* Other service screens */}
                <Stack.Screen
                  name="services/fine"
                  options={{
                    gestureEnabled: true,
                    presentation: "card",
                  }}
                />
                <Stack.Screen
                  name="services/booking"
                  options={{
                    gestureEnabled: true,
                    presentation: "card",
                  }}
                />
                <Stack.Screen
                  name="services/promotion"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />

                {/* Route and booking screens */}
                <Stack.Screen
                  name="route-preview"
                  options={{
                    gestureEnabled: true,
                    presentation: "card",
                  }}
                />
                <Stack.Screen
                  name="payment-method-selection"
                  options={{
                    gestureEnabled: true,
                    presentation: "modal",
                  }}
                />

                {/* External forms and utilities */}
                <Stack.Screen
                  name="external-form"
                  options={{
                    gestureEnabled: true,
                    presentation: "fullScreenModal",
                  }}
                />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </AuthProvider>
        </AppInitializer>
      </PersistGate>
    </Provider>
  );
}

// Redirect from root to auth screen
export function HomeRoute() {
  return <Redirect href="/auth" />;
}
