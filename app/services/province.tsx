import { getCoordinatesFromPlaceId } from "@/api/geocoding.api";
import { LocationSuggestion, searchLocations } from "@/api/location.api";
import { PaymentMethod } from "@/api/payment-methods.api";
import { Colors } from "@/constants/Colors";
import GoongConstants from "@/constants/GoongConstants";
import { useDebounce } from "@/hooks/useDebounce";
import useSettings from "@/hooks/useSettings";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchAllSettings,
  setDefaultSettings,
} from "@/store/slices/settingsSlice";
import {
  selectPaymentMethods,
  selectPaymentMethodsLoading,
} from "@/store/slices/paymentMethodsSlice";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { Camera, MapView } from "@maplibre/maplibre-react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as Location from "expo-location";
import { router, useLocalSearchParams } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  Keyboard,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Define interfaces for route data
interface LocationPoint {
  place_id: string;
  description: string;
}

// Danh sách các phương thức thanh toán sẽ được lấy từ API

function App() {
  // Make sure GoongConstants is defined before using it
  const [loadMap] = useState(() => {
    if (
      !GoongConstants ||
      !GoongConstants.MAP_TILES_URL ||
      !GoongConstants.MAP_KEY
    ) {
      console.error("GoongConstants is not properly defined:", GoongConstants);
      return "https://tiles.goong.io/assets/goong_map_web.json?api_key=H32hbWEAgDaXRTJzzT9hxHCU1S0YH9QxjX30SR5S";
    }
    return `${GoongConstants.MAP_TILES_URL}?api_key=${GoongConstants.MAP_KEY}`;
  });

  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  const [isSearchLoading, setIsSearchLoading] = useState(false);

  // Get payment methods from global state
  const paymentMethods = useAppSelector(selectPaymentMethods);
  const isPaymentMethodsLoading = useAppSelector(selectPaymentMethodsLoading);

  // Center coordinates for the map (longitude, latitude)
  const [coordinates, setCoordinates] = useState<[number, number]>([
    107.8091190568345, 11.54498921967426,
  ]);

  // State cho thanh tìm kiếm địa chỉ
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
  const [selectedLocation, setSelectedLocation] =
    useState<LocationPoint | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [pickupAddress, setPickupAddress] = useState("");

  // Tạo ngày mai, đặt giờ là 8:00 sáng
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(8, 0, 0, 0);

  // Danh sách tên tháng bằng tiếng Việt
  const vietnameseMonths = [
    "THÁNG 1",
    "THÁNG 2",
    "THÁNG 3",
    "THÁNG 4",
    "THÁNG 5",
    "THÁNG 6",
    "THÁNG 7",
    "THÁNG 8",
    "THÁNG 9",
    "THÁNG 10",
    "THÁNG 11",
    "THÁNG 12",
  ];

  // Format ngày mai thành chuỗi "HH:MM - DD/MM/YYYY"
  const formatDate = (date: Date) => {
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${hours}:${minutes} - ${day}/${month}/${year}`;
  };

  // State cho thời gian bắt đầu và số ngày đi
  const [startTime, setStartTime] = useState(formatDate(tomorrow));
  const [tripDays, setTripDays] = useState(1);

  // State cho phương thức thanh toán
  const [paymentMethod, setPaymentMethod] = useState("CASH"); // Mặc định là tiền mặt với code CASH

  // Lấy params từ URL
  const params = useLocalSearchParams();

  // Xử lý khi nhận được phương thức thanh toán từ màn hình chọn
  useEffect(() => {
    if (params.selectedPaymentMethod) {
      setPaymentMethod(params.selectedPaymentMethod as string);
    }
  }, [params.selectedPaymentMethod]);

  // State cho datetime picker
  const [date, setDate] = useState(tomorrow);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // State cho modal chọn số ngày
  const [showDaysModal, setShowDaysModal] = useState(false);
  const [customDays, setCustomDays] = useState("");
  const [showCustomDaysInput, setShowCustomDaysInput] = useState(false);

  // State cho modal xác nhận đặt xe
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Sử dụng Redux để lấy ghi chú và mã quảng cáo
  const { note, promoCode } = useAppSelector((state) => state.service);

  // Sử dụng hook để lấy cài đặt giá
  const dispatch = useAppDispatch();
  const { getSetting } = useSettings({ fetchOnMount: true });

  // State cho giá
  const [originalPrice, setOriginalPrice] = useState(0);
  const [discountedPrice, setDiscountedPrice] = useState(0);

  // Fetch settings khi component mount
  useEffect(() => {
    dispatch(fetchAllSettings())
      .unwrap()
      .catch(() => {
        dispatch(setDefaultSettings());
      });
  }, [dispatch]);

  // Tính giá dựa trên số ngày và cài đặt
  const calculatePrice = useCallback(() => {
    // Lấy giá từ cài đặt hoặc sử dụng giá mặc định
    const dayFee = getSetting("saygo_suburban_day_fee", 800000);
    const serviceFee = getSetting("saygo_suburban_service_fee", 30000);
    const holidayFee = getSetting("saygo_suburban_holiday_fee", 100000);

    // Kiểm tra xem ngày đã chọn có phải là ngày lễ không

    // Tính tổng giá
    let total = dayFee * tripDays + serviceFee + holidayFee;

    // Tính giá sau giảm giá (giả sử giảm 5%)
    const discount = 0;
    const discounted = Math.round(total * (1 - discount));

    // Cập nhật state
    setOriginalPrice(total);
    setDiscountedPrice(discounted);
  }, [tripDays, getSetting]);

  // Tính giá khi số ngày thay đổi hoặc cài đặt thay đổi
  useEffect(() => {
    calculatePrice();
  }, [tripDays, calculatePrice]);

  // Debounce search query để giảm số lần gọi API
  const debouncedQuery = useDebounce(searchQuery, 500); // 500ms delay

  const camera = useRef(null);
  const searchInputRef = useRef<TextInput>(null);
  const insets = useSafeAreaInsets();

  const handleBack = () => {
    // navigate to index screen
    router.back();
  };

  // Xử lý khi người dùng nhấp vào thời gian bắt đầu
  const handleTimePress = () => {
    // Hiển thị date picker trước
    setShowDatePicker(true);
  };

  // Xử lý khi người dùng nhấp vào số ngày đi
  const handleDaysPress = () => {
    setShowDaysModal(true);
  };

  // Các hàm xử lý đã được thay thế bằng inline functions trong DateTimePicker

  // Xử lý khi người dùng chọn số ngày đi
  const handleDaysSelect = (days: number) => {
    setTripDays(days);
    setShowDaysModal(false);
    setShowCustomDaysInput(false);
    // Tính lại giá khi số ngày thay đổi
    calculatePrice();
  };

  // Xử lý khi người dùng chọn tùy chọn tự nhập
  const handleCustomDaysSelect = () => {
    setShowCustomDaysInput(true);
  };

  // Xử lý khi người dùng xác nhận số ngày tự nhập
  const handleCustomDaysConfirm = () => {
    if (customDays.trim() !== "") {
      const days = parseInt(customDays);
      if (!isNaN(days) && days > 0) {
        setTripDays(days);
        // Tính lại giá khi số ngày thay đổi
        calculatePrice();
      }
    }
    setShowDaysModal(false);
    setShowCustomDaysInput(false);
    setCustomDays("");
  };

  // Xử lý tìm kiếm địa chỉ khi query thay đổi
  useEffect(() => {
    const fetchSuggestions = async () => {
      // Chỉ hiển thị gợi ý khi nhập tối thiểu 5 ký tự
      if (debouncedQuery.length < 5) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsSearchLoading(true);
      try {
        const results = await searchLocations(debouncedQuery, 5);
        setSuggestions(results);
        setShowSuggestions(true);
      } catch (error) {
        console.error("Error fetching suggestions:", error);
      } finally {
        setIsSearchLoading(false);
      }
    };

    fetchSuggestions();
  }, [debouncedQuery]);

  // Xử lý khi người dùng chọn một địa chỉ từ gợi ý - sử dụng useCallback để tối ưu hiệu suất
  const handleSelectLocation = useCallback(
    async (location: LocationSuggestion) => {
      // Cập nhật địa chỉ điểm đón ngay lập tức
      setPickupAddress(location.description);

      // Cập nhật giá trị trong thanh input
      setSearchQuery("");

      // Cập nhật location đã chọn
      setSelectedLocation({
        place_id: location.place_id,
        description: location.description,
      });

      // Ẩn danh sách gợi ý
      setSuggestions([]);
      setShowSuggestions(false);

      // Lấy tọa độ từ place_id
      try {
        const coords = await getCoordinatesFromPlaceId(location.place_id);
        if (coords) {
          // Cập nhật tọa độ trên bản đồ
          console.log("Coordinates:", coords);
          setCoordinates([coords.lng, coords.lat]);
          // Di chuyển camera đến vị trí mới
          if (camera.current) {
            // Sử dụng any để tránh lỗi TypeScript
            (camera.current as any).setCamera({
              centerCoordinate: [coords.lng, coords.lat],
              zoomLevel: 15,
              animationDuration: 500, // Giảm thời gian animation để tăng hiệu suất
            });
          }
        }
      } catch (error) {
        console.error("Error getting coordinates:", error);
      }
    },
    [camera]
  );

  return (
    <TouchableOpacity
      style={{ flex: 1 }}
      activeOpacity={1}
      onPress={() => {
        // Ẩn bàn phím khi nhấn vào bất kỳ đâu trên màn hình
        Keyboard.dismiss();
        // Ẩn danh sách gợi ý nếu đang hiển thị
        if (showSuggestions) {
          setShowSuggestions(false);
        }
      }}
    >
      <StatusBar hidden />
      <>
        <MapView
          mapStyle={loadMap}
          style={{ flex: 1 }}
          zoomEnabled={false}
          scrollEnabled={false} // Tắt cuộn để tăng hiệu suất
          pitchEnabled={false} // Tắt nghiêng để tăng hiệu suất
          rotateEnabled={false} // Tắt xoay để tăng hiệu suất
          logoEnabled={false}
          compassEnabled={false} // Tắt la bàn để tăng hiệu suất
          attributionEnabled={false} // Tắt attribution để tăng hiệu suất
          surfaceView={true} // Sử dụng SurfaceView trên Android để tăng hiệu suất
          onPress={() => {}} // Ngăn chặn sự kiện nhấn vào bản đồ thay đổi vị trí marker
          onLongPress={() => {}} // Ngăn chặn sự kiện nhấn giữ vào bản đồ
          onDidFinishLoadingMap={() => {
            // Ẩn loading khi bản đồ tải thành công
            setIsLoading(false);
          }}
          onDidFailLoadingMap={() => {
            console.error("MapView failed to load");
            setIsLoading(false); // Ẩn loading ngay cả khi có lỗi
            Alert.alert(
              "Lỗi bản đồ",
              "Không thể tải bản đồ. Vui lòng thử lại sau."
            );
          }}
        >
          <Camera
            ref={camera}
            zoomLevel={16} // Mức thu phóng của bản đồ
            centerCoordinate={coordinates}
            animationDuration={0} // Tắt animation để tăng hiệu suất
            followUserLocation={false} // Không theo dõi vị trí người dùng
          />
          {/* Marker tùy chỉnh thay thế PointAnnotation */}
          <View
            style={[
              styles.customMarkerWrapper,
              {
                left: "50%",
                top: "50%",
                marginLeft: -15, // Một nửa chiều rộng của marker
                marginTop: -30, // Chiều cao của marker để đặt đúng vị trí
              },
            ]}
          >
            <View style={styles.markerContainer}>
              <Text style={styles.markerText}>P</Text>
            </View>
          </View>
        </MapView>

        {/* Bottom action bar */}
        <View style={styles.bottomContainer}>
          <View style={styles.tripInfoContainer}>
            <TouchableOpacity
              style={styles.tripInfoItem}
              onPress={handleTimePress}
            >
              <Text style={styles.tripInfoLabel}>Thời gian bắt đầu</Text>
              <Text style={styles.tripInfoValue}>{startTime}</Text>
              <Ionicons
                name="chevron-down"
                size={16}
                color="#666"
                style={styles.tripInfoIcon}
              />
            </TouchableOpacity>
            <View style={styles.tripInfoDivider} />
            <TouchableOpacity
              style={styles.tripInfoItem}
              onPress={handleDaysPress}
            >
              <Text style={styles.tripInfoLabel}>Số ngày thuê tài</Text>
              <Text style={styles.tripInfoValue}>{tripDays} ngày</Text>
              <Ionicons
                name="chevron-down"
                size={16}
                color="#666"
                style={styles.tripInfoIcon}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.actionButtonsContainer}>
            <View style={styles.leftButtonsGroup}>
              {/* Phương thức thanh toán */}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() =>
                  router.push({
                    pathname: "/payment-method-selection",
                    params: { currentPaymentMethod: paymentMethod },
                  })
                }
              >
                {(() => {
                  const method = paymentMethods.find(
                    (m: PaymentMethod) => m.code === paymentMethod
                  );
                  if (!method) {
                    // Show skeleton loading or default if payment methods are not loaded yet
                    return isPaymentMethodsLoading ? (
                      <View style={styles.paymentMethodButtonContent}>
                        <View style={styles.skeletonIconContainer}>
                          <View style={styles.skeletonIcon} />
                        </View>
                        <View style={styles.skeletonTextContainer}>
                          <View style={styles.skeletonText} />
                        </View>
                      </View>
                    ) : (
                      <Text style={styles.actionButtonText}>Tiền mặt</Text>
                    );
                  }

                  return (
                    <View style={styles.paymentMethodButtonContent}>
                      {/* Display payment method icon from URL */}
                      <View style={styles.paymentMethodIconContainer}>
                        <Image
                          source={{ uri: method.icon }}
                          style={styles.paymentMethodIcon}
                          resizeMode="contain"
                        />
                      </View>
                      <Text style={styles.actionButtonText}>{method.name}</Text>
                    </View>
                  );
                })()}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push("/services/province-promotion")}
              >
                <Text style={styles.actionButtonText}>Ưu đãi</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push("/services/province-notes")}
              >
                <Text style={styles.actionButtonText}>Ghi chú</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.actionButton, styles.iconButton]}
              onPress={() => router.push("/services/province-about")}
            >
              <Ionicons name="ellipsis-horizontal" size={18} color="#333" />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.findDriverButton}
            onPress={() => setShowConfirmModal(true)}
          >
            <View style={styles.priceContainer}>
              {originalPrice !== discountedPrice && (
                <Text style={styles.originalPrice}>
                  {originalPrice.toLocaleString()}đ
                </Text>
              )}
              <Text style={styles.discountedPrice}>
                {discountedPrice.toLocaleString()}đ
              </Text>
            </View>
            <View style={styles.buttonTextContainer}>
              <Text style={styles.buttonText}>Thuê tài xế {tripDays} ngày</Text>
              <MaterialIcons name="arrow-forward" size={20} color="#333" />
            </View>
          </TouchableOpacity>
        </View>

        {/* Thanh tìm kiếm địa chỉ kết hợp hiển thị điểm đón */}
        <View style={[styles.searchContainer, { top: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>

          <View style={styles.searchInputContainer}>
            {showSuggestions ? (
              <>
                <Ionicons
                  name="search"
                  size={20}
                  color="#666"
                  style={{ marginRight: 8 }}
                />
                <TextInput
                  ref={searchInputRef}
                  style={styles.searchInput}
                  placeholder="Tìm kiếm địa chỉ..."
                  placeholderTextColor="#666"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  returnKeyType="search"
                  onSubmitEditing={() => searchInputRef.current?.blur()}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => {
                      setSearchQuery("");
                      setShowSuggestions(false);
                    }}
                  >
                    <Ionicons name="close-circle" size={20} color="#999" />
                  </TouchableOpacity>
                )}
              </>
            ) : (
              <TouchableOpacity
                style={styles.pickupDisplayContainer}
                onPress={() => {
                  if (pickupAddress) {
                    setSearchQuery(pickupAddress);
                  }
                  setShowSuggestions(true);
                  searchInputRef.current?.focus();
                }}
              >
                <Ionicons
                  name="location"
                  size={20}
                  color="#ffde59"
                  style={{ marginRight: 8 }}
                />
                <View style={styles.pickupAddressTextContainer}>
                  <Text style={styles.pickupAddressLabel}>Điểm đón</Text>
                  {pickupAddress ? (
                    <Text style={styles.pickupAddressText} numberOfLines={1}>
                      {pickupAddress}
                    </Text>
                  ) : (
                    <Text style={styles.pickupAddressPlaceholder}>
                      Chọn địa điểm đón
                    </Text>
                  )}
                </View>
                <Ionicons name="pencil" size={18} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {showSuggestions &&
          searchQuery.length >= 5 &&
          suggestions.length > 0 && (
            <View
              style={[styles.suggestionsContainer, { top: insets.top + 72 }]}
              pointerEvents="box-none" // Cho phép sự kiện chạm xuyên qua khi cần thiết
            >
              {isSearchLoading ? (
                <ActivityIndicator
                  size="small"
                  color="#007AFF"
                  style={styles.loader}
                />
              ) : (
                <FlatList
                  data={suggestions}
                  keyExtractor={(item) => item.place_id}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.suggestionItem}
                      onPress={() => handleSelectLocation(item)}
                      activeOpacity={0.5} // Giảm độ mờ khi nhấn để phản hồi tốt hơn
                    >
                      <Ionicons
                        name="location-outline"
                        size={18}
                        color="#666"
                        style={styles.suggestionIcon}
                      />
                      <Text style={styles.suggestionText} numberOfLines={2}>
                        {item.description}
                      </Text>
                    </TouchableOpacity>
                  )}
                  style={styles.suggestionsList}
                  nestedScrollEnabled
                  maxToRenderPerBatch={5}
                  initialNumToRender={5}
                  keyboardShouldPersistTaps="handled" // Đảm bảo tap vẫn hoạt động khi bàn phím hiển thị
                />
              )}
            </View>
          )}

        {/* Modal cho DateTimePicker */}
        {(showDatePicker || showTimePicker) && (
          <View style={styles.modalOverlay}>
            <View
              style={[styles.modalContainer, styles.dateTimePickerContainer]}
            >
              <View style={styles.dateTimePickerHeader}>
                <Text style={styles.dateTimePickerTitle}>
                  {showDatePicker ? "Chọn ngày" : "Chọn giờ"}
                </Text>
                <TouchableOpacity
                  style={styles.dateTimePickerCloseButton}
                  onPress={() => {
                    if (showDatePicker) {
                      setShowDatePicker(false);
                    } else {
                      setShowTimePicker(false);
                    }
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.dateTimePickerWrapper}>
                {/* DateTimePicker cho ngày với tháng tiếng Việt */}
                {showDatePicker && (
                  <View style={styles.customDatePickerContainer}>
                    <View style={styles.monthYearContainer}>
                      <Text style={styles.monthYearText}>
                        {vietnameseMonths[date.getMonth()]} {date.getFullYear()}
                      </Text>
                    </View>
                    <DateTimePicker
                      testID="datePicker"
                      value={date}
                      mode="date"
                      display={Platform.OS === "ios" ? "spinner" : "default"}
                      onChange={(_, selectedDate) => {
                        if (selectedDate) {
                          // Cập nhật state ngay lập tức
                          setDate(selectedDate);
                        }
                      }}
                      style={styles.dateTimePicker}
                      locale="vi-VN" // Sử dụng locale tiếng Việt nếu có hỗ trợ
                      minimumDate={
                        new Date(
                          tomorrow.getFullYear(),
                          tomorrow.getMonth(),
                          tomorrow.getDate()
                        )
                      } // Giới hạn ngày chọn không nhỏ hơn ngày mai
                      maximumDate={
                        new Date(
                          tomorrow.getFullYear(),
                          tomorrow.getMonth(),
                          tomorrow.getDate() + 13
                        )
                      } // Giới hạn ngày chọn không quá 14 ngày kể từ ngày mai
                      themeVariant="light"
                    />
                    <View style={styles.datePickerNoteContainer}>
                      <Text style={styles.datePickerNoteText}>
                        Chỉ được đặt trước tối đa 14 ngày
                      </Text>
                    </View>
                  </View>
                )}

                {/* DateTimePicker cho giờ */}
                {showTimePicker && (
                  <DateTimePicker
                    testID="timePicker"
                    value={date}
                    mode="time"
                    display={Platform.OS === "ios" ? "spinner" : "default"}
                    onChange={(_, selectedTime) => {
                      if (selectedTime) {
                        // Chỉ cập nhật state tạm thời, chưa áp dụng
                        setDate(selectedTime);
                      }
                    }}
                    style={styles.dateTimePicker}
                    themeVariant="light"
                  />
                )}
              </View>

              {/* Nút xác nhận - hiển thị cho cả iOS và Android */}
              <TouchableOpacity
                style={styles.confirmDateTimeButton}
                onPress={() => {
                  // Tạo ngày tối đa (14 ngày kể từ ngày mai)
                  const maxDate = new Date(
                    tomorrow.getFullYear(),
                    tomorrow.getMonth(),
                    tomorrow.getDate() + 13
                  );

                  // Kiểm tra xem ngày đã chọn có vượt quá giới hạn không
                  const selectedDate = new Date(
                    date.getFullYear(),
                    date.getMonth(),
                    date.getDate()
                  );

                  if (selectedDate > maxDate) {
                    // Hiển thị thông báo lỗi nếu ngày vượt quá giới hạn
                    Alert.alert(
                      "Thông báo",
                      "Bạn chỉ có thể đặt trước tối đa 14 ngày kể từ ngày mai.",
                      [{ text: "Đã hiểu", style: "default" }]
                    );
                    return;
                  }

                  if (showDatePicker) {
                    // Chuyển từ chọn ngày sang chọn giờ
                    setShowDatePicker(false);
                    setShowTimePicker(true);
                  } else {
                    // Hoàn tất việc chọn thời gian
                    setShowTimePicker(false);
                    setStartTime(formatDate(date));
                  }
                }}
              >
                <Text style={styles.confirmDateTimeButtonText}>
                  {showDatePicker ? "Tiếp theo" : "Xác nhận"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {showDaysModal && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Chọn số ngày thuê tài</Text>
                <TouchableOpacity onPress={() => setShowDaysModal(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              <View style={styles.modalContent}>
                <ScrollView
                  style={styles.modalScrollView}
                  contentContainerStyle={styles.modalScrollContent}
                >
                  {[1, 2, 3, 4, 5, 6, 7].map((days) => (
                    <TouchableOpacity
                      key={days}
                      style={[
                        styles.modalItem,
                        days === tripDays && styles.modalItemSelected,
                      ]}
                      onPress={() => handleDaysSelect(days)}
                    >
                      <Text
                        style={[
                          styles.modalItemText,
                          days === tripDays && styles.modalItemTextSelected,
                        ]}
                      >
                        {days} ngày
                      </Text>
                      {days === tripDays && (
                        <Ionicons name="checkmark" size={20} color="#007AFF" />
                      )}
                    </TouchableOpacity>
                  ))}

                  {/* Tùy chọn tự nhập */}
                  {!showCustomDaysInput ? (
                    <TouchableOpacity
                      style={styles.modalItem}
                      onPress={handleCustomDaysSelect}
                    >
                      <Text style={styles.modalItemText}>Tự nhập số ngày</Text>
                      <Ionicons name="create-outline" size={20} color="#666" />
                    </TouchableOpacity>
                  ) : (
                    <View style={styles.customDaysContainer}>
                      <View style={{ flex: 1, marginRight: 10 }}>
                        <TextInput
                          style={styles.customDaysInput}
                          placeholder="Nhập số ngày"
                          value={customDays}
                          onChangeText={setCustomDays}
                          keyboardType="number-pad"
                          autoFocus
                          maxLength={3} // Giới hạn số ký tự nhập
                        />
                      </View>
                      <TouchableOpacity
                        style={styles.customDaysButton}
                        onPress={handleCustomDaysConfirm}
                      >
                        <Text style={styles.customDaysButtonText}>
                          Xác nhận
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </ScrollView>
              </View>
            </View>
          </View>
        )}

        {/* Modal xác nhận đặt xe */}
        {showConfirmModal && (
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContainer, styles.confirmModalContainer]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Xác nhận thông tin</Text>
                <TouchableOpacity onPress={() => setShowConfirmModal(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.confirmModalContent}>
                {/* Thông tin thời gian bắt đầu */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Thời gian bắt đầu:</Text>
                  <Text style={styles.confirmValue}>{startTime}</Text>
                </View>

                {/* Thông tin số ngày thuê */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Số ngày thuê:</Text>
                  <Text style={styles.confirmValue}>{tripDays} ngày</Text>
                </View>

                {/* Thông tin địa chỉ đón */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Địa chỉ đón:</Text>
                  <Text style={styles.confirmValue}>
                    {pickupAddress || "Chưa chọn địa chỉ"}
                  </Text>
                </View>

                {/* Thông tin ưu đãi */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Ưu đãi:</Text>
                  <Text style={styles.confirmValue}>
                    {promoCode || "Không có"}
                  </Text>
                </View>

                {/* Thông tin ghi chú */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Ghi chú:</Text>
                  <Text style={styles.confirmValue}>{note || "Không có"}</Text>
                </View>

                {/* Thông tin phương thức thanh toán */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>
                    Phương thức thanh toán:
                  </Text>
                  <Text style={styles.confirmValue}>
                    {paymentMethods.find(
                      (method: PaymentMethod) => method.code === paymentMethod
                    )?.name || "Tiền mặt"}
                  </Text>
                </View>

                {/* Thông tin giá */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Giá gốc:</Text>
                  <Text style={styles.confirmValue}>
                    {originalPrice.toLocaleString()}đ
                  </Text>
                </View>

                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Giá sau ưu đãi:</Text>
                  <Text style={[styles.confirmValue, styles.discountPrice]}>
                    {discountedPrice.toLocaleString()}đ
                  </Text>
                </View>

                {/* Hiển thị chi tiết tính giá */}
                <View style={styles.confirmItem}>
                  <Text style={styles.confirmLabel}>Chi tiết tính giá:</Text>
                  <Text style={styles.confirmValue}>
                    (
                    {getSetting(
                      "saygo_suburban_day_fee",
                      800000
                    ).toLocaleString()}{" "}
                    × {tripDays} ngày) +{" "}
                    {getSetting(
                      "saygo_suburban_service_fee",
                      30000
                    ).toLocaleString()}{" "}
                    phí dịch vụ
                  </Text>
                </View>
              </ScrollView>

              <View style={styles.confirmButtonContainer}>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => {
                    setShowConfirmModal(false);
                    alert("Đã đặt xe thành công!");
                  }}
                >
                  <Text style={styles.confirmButtonText}>XÁC NHẬN ĐẶT XE</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </>
    </TouchableOpacity>
  );
}

// Sử dụng React.memo để tránh re-render không cần thiết
export default React.memo(App);

const styles = StyleSheet.create({
  backButton: {
    backgroundColor: "white",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  currentLocationButton: {
    backgroundColor: "white",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    marginLeft: 10,
  },
  // Styles cho thanh tìm kiếm
  searchContainer: {
    position: "absolute",
    left: 16,
    right: 16,
    flexDirection: "row",
    alignItems: "center",
    zIndex: 10,
  },
  searchInputContainer: {
    flex: 1,
    marginLeft: 6,
    backgroundColor: "white",
    borderRadius: 8,
    minHeight: 50,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: "#333",
    fontWeight: 400, // Làm đậm chữ nhập vào
  },
  clearButton: {
    padding: 5,
  },
  pickupDisplayContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    height: 40,
    paddingVertical: 5,
  },
  // Styles cho danh sách gợi ý
  suggestionsContainer: {
    position: "absolute",
    left: 16,
    right: 16,
    backgroundColor: "white",
    borderRadius: 12,
    maxHeight: "80%", // Tăng chiều cao tối đa để hiển thị nhiều gợi ý hơn
    zIndex: 999, // Tăng z-index để đảm bảo hiển thị trên các phần tử khác
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5, // Tăng elevation để đảm bảo hiển thị trên Android
  },
  loader: {
    padding: 15,
    alignSelf: "center",
  },
  suggestionsList: {
    maxHeight: "100%", // Sử dụng toàn bộ chiều cao của container
    width: "100%", // Sử dụng toàn bộ chiều rộng của container
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  suggestionIcon: {
    marginRight: 15,
  },
  suggestionText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#333",
  },
  // Marker styles
  markerContainer: {
    width: 30,
    height: 30,
    borderRadius: 18,
    backgroundColor: "#ffde59", // Màu primary
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
    // Giảm shadow để tăng hiệu suất
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {
        elevation: 3, // Giảm elevation trên Android
      },
    }),
  },
  markerText: {
    color: "#333", // Màu chữ đen để tương phản với nền vàng
    fontWeight: "bold",
    fontSize: 14,
    textAlign: "center",
    fontFamily: "Nunito",
  },
  // Tối ưu hiệu suất bằng cách giảm shadow và hiệu ứng
  customMarkerWrapper: {
    position: "absolute",
    zIndex: 1,
    pointerEvents: "none", // Cho phép các sự kiện chạm xuyên qua marker
  },
  // Pickup card styles
  pickupCard: {
    position: "absolute",
    top: 120, // Tăng top để không bị đè lên bởi thanh tìm kiếm
    left: 16,
    right: 16,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pickupInfo: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  pickupDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#00C853",
    marginTop: 4,
    marginRight: 12,
  },
  pickupTextContainer: {
    flex: 1,
  },
  pickupLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  pickupAddress: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  // Trip info styles
  tripInfoContainer: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  tripInfoItem: {
    flex: 1,
    padding: 12,
    alignItems: "center",
  },
  tripInfoDivider: {
    width: 1,
    backgroundColor: "#E0E0E0",
  },
  tripInfoLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  tripInfoValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  tripInfoIcon: {
    marginTop: 4,
  },
  // Modal styles
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  modalContainer: {
    width: "90%",
    backgroundColor: "white",
    borderRadius: 12,
    overflow: "hidden",
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  modalContent: {
    padding: 16,
    height: 450, // Chiều cao cố định vừa đủ cho các tùy chọn
  },
  modalScrollView: {
    flex: 1,
  },
  modalScrollContent: {
    paddingBottom: 10,
  },
  modalItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  modalItemSelected: {
    backgroundColor: "#f0f8ff",
  },
  modalItemText: {
    fontSize: 16,
    color: "#333",
  },
  modalItemTextSelected: {
    color: Colors.primary,
    fontWeight: "600",
  },
  // Custom days input styles
  customDaysContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    height: 68, // Tăng chiều cao để đủ không gian cho nút
  },
  customDaysInput: {
    height: 40,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    width: "100%",
  },
  customDaysButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    minWidth: 100,
    height: 44,
  },
  customDaysButtonText: {
    color: Colors.white,
    fontWeight: "600",
    fontSize: 16,
    textAlign: "center",
    lineHeight: 20,
  },
  // Bottom container styles
  bottomContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#FFF8F3",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingBottom: 48, // Tăng padding bottom để tạo thêm khoảng cách
  },
  carIconContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  findDriverText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginLeft: 10,
  },
  actionButtonsContainer: {
    flexDirection: "row",
    marginBottom: 20, // Tăng margin bottom để tạo thêm khoảng cách với nút đặt
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftButtonsGroup: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "white",
    borderRadius: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    shadowRadius: 2,
    elevation: 2,
    justifyContent: "center",
    alignItems: "center",
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    textAlign: "center",
  },
  iconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 0,
    marginRight: 0,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  findDriverButton: {
    backgroundColor: "#ffde59", // Màu primary
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  priceContainer: {
    flexDirection: "column",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  originalPrice: {
    fontSize: 14,
    color: "#999",
    textDecorationLine: "line-through",
  },
  discountedPrice: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  buttonTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginRight: 8,
  },
  // Styles cho modal xác nhận
  confirmModalContainer: {
    width: "90%",
    maxHeight: "80%",
  },
  confirmModalContent: {
    padding: 16,
  },
  confirmItem: {
    marginBottom: 16,
  },
  confirmLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  confirmValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  discountPrice: {
    color: "#FF6B00",
    fontWeight: "bold",
  },
  confirmButtonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  confirmButton: {
    backgroundColor: "#ffde59",
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
  },
  // Styles cho datetime picker modal
  dateTimePickerContainer: {
    width: "90%",
    padding: 16,
    alignItems: "center",
  },
  dateTimePickerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    paddingHorizontal: 0,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  dateTimePickerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    textAlign: "left",
  },
  dateTimePickerCloseButton: {
    padding: 5,
  },
  dateTimePickerWrapper: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 20,
  },
  // Styles cho custom date picker với tháng tiếng Việt
  customDatePickerContainer: {
    width: "100%",
    alignItems: "center",
  },
  monthYearContainer: {
    width: "100%",
    alignItems: "center",
    marginBottom: 10,
    paddingVertical: 8,
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
  },
  monthYearText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  dateTimePicker: {
    width: Platform.OS === "ios" ? "100%" : 300,
    height: Platform.OS === "ios" ? 200 : 150,
  },
  // Styles cho ghi chú giới hạn ngày
  datePickerNoteContainer: {
    marginTop: 10,
    paddingHorizontal: 16,
    width: "100%",
    alignItems: "center",
  },
  datePickerNoteText: {
    fontSize: 14,
    color: "#666",
    fontStyle: "italic",
  },
  confirmDateTimeButton: {
    backgroundColor: "#ffde59", // Màu primary
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    width: "80%",
  },
  confirmDateTimeButtonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
  },
  // Styles cho hiển thị điểm đón
  pickupAddressContainer: {
    position: "absolute",
    left: 16,
    right: 16,
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 5,
  },
  pickupAddressContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
  },
  pickupAddressIcon: {
    marginRight: 12,
  },
  pickupAddressTextContainer: {
    flex: 1,
  },
  pickupAddressLabel: {
    fontSize: 12,
    color: "#666",
    marginBottom: 4,
  },
  pickupAddressText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  pickupAddressPlaceholder: {
    fontSize: 14,
    fontWeight: "400",
    color: "#999",
    fontStyle: "italic",
  },
  pickupAddressEditButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
  },
  // Styles cho phương thức thanh toán
  paymentMethodContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  paymentMethodHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  selectedPaymentMethod: {
    marginTop: 4,
  },
  selectedPaymentMethodContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  selectedPaymentMethodIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  selectedPaymentMethodText: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  // Styles for payment method button in action buttons row
  paymentMethodButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  paymentMethodIconContainer: {
    width: 24,
    height: 24,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  paymentMethodIcon: {
    width: 20,
    height: 20,
  },
  paymentMethodButtonIcon: {
    marginRight: 4,
  },
  // Skeleton styles
  skeletonIconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#f5f5f5",
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  skeletonIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: "#e0e0e0",
  },
  skeletonTextContainer: {
    height: 14,
    width: 60,
    justifyContent: "center",
  },
  skeletonText: {
    height: 10,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    width: "100%",
  },

  // Styles for old payment method buttons (keeping for reference)
  paymentButtonsContainer: {
    paddingRight: 8,
    paddingBottom: 4,
  },
  paymentMethodButton: {
    width: 90,
    height: 70,
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
    padding: 8,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
    borderWidth: 1,
    borderColor: "#eee",
  },
  paymentMethodButtonActive: {
    backgroundColor: "#fff8e1",
    borderColor: Colors.primary,
  },
  paymentMethodText: {
    fontSize: 11,
    color: "#666",
    marginTop: 6,
    textAlign: "center",
    width: "100%",
  },
  paymentMethodTextActive: {
    color: "#333",
    fontWeight: "600",
  },
});
